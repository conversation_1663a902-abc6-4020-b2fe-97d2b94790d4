import numpy as np
from PIL import Image

def read_image(image_path):
    """读取图片并返回其像素数据"""
    try:
        # 打开图片
        with Image.open(image_path) as img:
            # 转换为 numpy 数组
            img_array = np.array(img)
            return img_array
    except FileNotFoundError:
        print(f"错误：找不到文件 '{image_path}'")
        return None
    except Exception as e:
        print(f"错误：无法读取图片 - {str(e)}")
        return None

def output_image_data(image_array, max_display_size=100):
    """输出图片数据值"""
    if image_array is None:
        return

    # 打印图片基本信息
    print(f"图片形状: {image_array.shape}")
    print(f"数据类型: {image_array.dtype}")

    # 根据图片维度提供不同的输出格式
    if len(image_array.shape) == 2:  # 灰度图
        print("图片数据（灰度值）:")
        print_array_with_truncation(image_array, max_display_size)
    elif len(image_array.shape) == 3:  # RGB 或 RGBA 图
        if image_array.shape[2] == 3:
            print("图片数据（RGB 值）:")
        else:
            print("图片数据（RGBA 值）:")
        print_array_with_truncation(image_array, max_display_size)
    else:
        print(f"不支持的图片维度: {len(image_array.shape)}")

def print_array_with_truncation(arr, max_size):
    """打印数组，数据量太大时进行截断"""
   
    print(arr)
        


if __name__ == "__main__":
    # 请替换为你自己的图片路径
    image_path = "000.png"
    image_data = read_image(image_path)
    output_image_data(image_data)    