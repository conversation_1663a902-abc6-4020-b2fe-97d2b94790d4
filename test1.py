# from PIL import Image, ImageOps
 
# img_path = '000.png'
 
# img = Image.open(img_path)
# img = ImageOps.autocontrast(img)    # 应用自动对比度
 
# img.show()  # 展示图像
# img.save("example.png")

import os
from PIL import Image, ImageOps

def process_images(input_dir, output_dir, show_images=False):
    """
    批量处理图片：应用自动对比度并保存结果
    
    参数:
    input_dir (str): 输入图片文件夹路径
    output_dir (str): 输出图片文件夹路径
    show_images (bool): 是否显示处理后的图片
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 支持的图片格式
    supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.webp'}
    
    # 获取所有图片文件
    image_files = [f for f in os.listdir(input_dir) 
                  if os.path.isfile(os.path.join(input_dir, f)) 
                  and os.path.splitext(f)[1].lower() in supported_formats]
    
    if not image_files:
        print(f"在目录 '{input_dir}' 中未找到支持的图片文件")
        return
    
    print(f"找到 {len(image_files)} 张图片")
    
    # 处理每张图片
    for filename in image_files:
        try:
            # 构建完整路径
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, filename)
            
            # 打开并处理图片
            with Image.open(input_path) as img:
                img = ImageOps.autocontrast(img)
                
                # 保存图片
                img.save(output_path)
                
                # 可选：显示图片
                if show_images:
                    img.show()
                    
            print(f"已处理: {filename} -> {output_path}")
            
        except Exception as e:
            print(f"处理图片 '{filename}' 时出错: {str(e)}")

if __name__ == "__main__":
    # 配置参数
    INPUT_DIR = "visa/pipe_fryum/Data/Masks/Anomaly"    # 输入文件夹
    OUTPUT_DIR = "visa/pipe_fryum/Masks/Anomaly"  # 输出文件夹
    SHOW_IMAGES = False           # 是否显示处理后的图片
    
    # 执行批量处理
    process_images(INPUT_DIR, OUTPUT_DIR, SHOW_IMAGES)    